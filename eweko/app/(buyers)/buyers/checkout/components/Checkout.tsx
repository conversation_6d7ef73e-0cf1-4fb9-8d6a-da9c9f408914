'use client';

import centiiv from '@/app/assets/centiiv.png';
import paystack from '@/app/assets/paystack.png';
import axios from 'axios';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FC, useEffect, useState } from 'react';
import { FaCheck, FaPlus, FaRegCreditCard, FaTruck } from 'react-icons/fa6';
import { HiPencil } from 'react-icons/hi';
import { IoIosCheckmark } from 'react-icons/io';
import { IoReturnUpBack } from 'react-icons/io5';
import { Bounce, toast } from 'react-toastify';
import {
  useSafeMutation,
  useSafeQuery,
} from '../../../../../axios/query-client';
import { Button } from '../../../../../components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from '../../../../../components/ui/dialog';
import { Label } from '../../../../../components/ui/label';
import {
  RadioGroup,
  RadioGroupItem,
} from '../../../../../components/ui/radio-group';
import { Separator } from '../../../../../components/ui/separator';
import { useGlobalState } from '../../../../../globalStore';
import { capitalizeFirstLetter } from '../../../../../lib/utils';
import { AllRoutes } from '../../../../../routes';
import { InlineLoader } from '../../../../components/InlineLoader';
import {
  Address,
  Cart,
  CreateOrder,
  CreateTransaction,
  Order,
  Transaction,
  TransactionResponse,
} from '../../types';
import { AddAddressForm } from './AddAddress';
import { EditAddressForm } from './EditAddress';
import type PaystackInline from '@paystack/inline-js'; // Import type only

// Define proper type for the Paystack module
type PaystackModule = typeof PaystackInline | null;

export const Checkout = () => {
  const router = useRouter();

  const paths = useSearchParams();
  const trxref = paths.get('trxref');
  const reference = paths.get('reference');
  const type = paths.get('type');
  const payment_method = paths.get('payment_method');
  const id = paths.get('id');
  const status = paths.get('status');

  const {
    addressStep,
    selectedAddress,
    selectedDelivery,
    selectedPayment,
    isDeliveryOpen,
    isPaymentOpen,
    setIsDeliveryOpen,
    setIsPaymentOpen,
    setSelectedDelivery,
    setSelectedPayment,
    setSelectedAddress,
    setAddress2edit,
    setCplId,
    setCpoId,
    setCptId,
    cart,
    setCart,
    loggedInUser,
  } = useGlobalState();

  const [shippingAddress, setShippingAddress] = useState('');
  const [cartSubTotal, setCartSubTotal] = useState(0);
  const [shipping, setShipping] = useState(0);
  const [checkoutTotal, setCheckoutTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckoutReady, setIsCheckoutReady] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const [transactionRef, setTransactionRef] = useState<string | undefined>(
    undefined
  );

  const [paystackModule, setPaystackModule] = useState<PaystackModule>(null);

  useEffect(() => {
    if (cart && cart.items && cart.items.length > 0) {
      const total = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);
      const vatRate = 7.5 / 100;
      const vatAmount = vatRate * total;
      const shipRate = 3.5 / 100;
      const shipping = total * shipRate;
      const checkoutTotal = total + vatAmount + shipping;
      setCartSubTotal(total);
      setShipping(shipping);
      setCheckoutTotal(checkoutTotal);
    }
  }, [cart]);

  // Load Paystack only on client-side
  useEffect(() => {
    const loadPaystack = async () => {
      try {
        const PaystackModule = (await import('@paystack/inline-js')).default;
        setPaystackModule(() => PaystackModule); // Store the constructor function
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to load Paystack module:', error);
      }
    };

    loadPaystack();
  }, []);

  const { mutate: createOrder } = useSafeMutation<Order, Error, CreateOrder>(
    AllRoutes.orders,
    'post',
    {}
  );

  const handleCreateOrder = (orderData: CreateOrder) => {
    setIsLoading(true);

    createOrder(orderData, {
      onSuccess: data => {
        setOrderId(data.id);

        toast.success('Order created successfully');
      },
      onError: (error: any) => {
        // console.error('Error creating order:', error);

        const errorMessage =
          error?.message || error?.errorMessage || 'Something went wrong';

        toast.error(errorMessage);

        setIsLoading(false);
      },
    });
  };

  const { mutate: createTransaction } = useSafeMutation<
    Transaction,
    Error,
    CreateTransaction
  >(AllRoutes.transactions, 'post', {});

  const handleCreateTransaction = (transactionData: CreateTransaction) => {
    setIsLoading(true);

    createTransaction(transactionData, {
      onSuccess: data => {
        setTransactionId(data.id);
        setTransactionRef(data.reference);

        toast.success('Transaction created successfully');

        // setIsLoading(false);
      },
      onError: (error: any) => {
        // console.error('Error creating transaction:', error);

        const errorMessage =
          error?.message || error?.errorMessage || 'Something went wrong';

        toast.error(errorMessage);

        setIsLoading(false);
      },
    });
  };

  const handleCheckout = async () => {
    setIsLoading(true);

    if (selectedPayment && selectedPayment === 'centiiv') {
      setIsLoading(false);
      if (
        loggedInUser?.id &&
        selectedAddress &&
        selectedDelivery &&
        cart &&
        cart.items.length > 0
      ) {
        const data = {
          userId: loggedInUser?.id,
          items: cart.items.map(item => ({
            produce: item.produce.id,
            quantity: item.quantity,
            totalPrice: item.totalPrice,
          })),
          shippingAddress,
          paymentMethod: 'Centiiv',
          totalCost: cartSubTotal,
          shippingFee: shipping,
          finalTotalCost: checkoutTotal,
        };

        handleCreateOrder(data);
      }
    } else {
      if (
        loggedInUser?.id &&
        selectedAddress &&
        selectedDelivery &&
        cart &&
        cart.items.length > 0
      ) {
        const data = {
          userId: loggedInUser?.id,
          items: cart.items.map(item => ({
            produce: item.produce.id,
            quantity: item.quantity,
            totalPrice: item.totalPrice,
          })),
          shippingAddress,
          paymentMethod: 'Paystack',
          totalCost: cartSubTotal,
          shippingFee: shipping,
          finalTotalCost: checkoutTotal,
        };

        handleCreateOrder(data);
      }
    }
  };

  const handleChange = (value: string) => {
    setSelectedDelivery(value);
    if (value === 'pickup') {
      setShippingAddress('126B, Aggregates Road, Eweko, Epe');
    } else if (value === 'doorstep') {
      setShippingAddress(
        `${selectedAddress?.houseNumber}, ${selectedAddress?.streetName}, ${selectedAddress?.community}, ${selectedAddress?.lga}, ${selectedAddress?.state}`
      );
    }
    setIsDeliveryOpen(false);
    setIsPaymentOpen(true);
  };

  const handlePaymentSelect = (value: string) => {
    setSelectedPayment(value);
    setIsDeliveryOpen(false);
    setIsPaymentOpen(true);
  };

  useEffect(() => {
    if (selectedAddress && selectedDelivery && selectedPayment) {
      setIsCheckoutReady(true);
    }
  }, [selectedAddress, selectedDelivery, selectedPayment]);

  useEffect(() => {
    if (orderId && loggedInUser?.id) {
      const data2: CreateTransaction = {
        user: loggedInUser?.id,
        order: orderId,
        paymentMethod:
          selectedPayment === 'paystack'
            ? 'Paystack'
            : selectedPayment === 'centiiv'
              ? 'Centiiv'
              : 'Unknown Payment Method',
        paymentType: 'Purchase',
        totalAmount: checkoutTotal,
      };

      handleCreateTransaction(data2);
    }
  }, [orderId, loggedInUser?.id]);

  useEffect(() => {
    if (orderId && transactionId && loggedInUser?.email) {
      if (selectedPayment === 'paystack') {
        if (!paystackModule) {
          alert('Paystack is not ready yet. Please try again.');
          return;
        }

        try {
          const amount = checkoutTotal * 100; // Paystack requires amount in kobo/cents
          const email = loggedInUser.email;
          const reference = transactionRef;

          // Format metadata according to Paystack's expected structure
          const metadata = {
            custom_fields: [
              {
                display_name: 'Transaction ID',
                variable_name: 'transactionid',
                value: transactionId,
              },
              {
                display_name: 'Buyer ID',
                variable_name: 'buyerid',
                value: loggedInUser.id,
              },
              {
                display_name: 'Order ID',
                variable_name: 'orderid',
                value: orderId,
              },
            ],
          };

          const publicKey = process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY;

          if (!publicKey) {
            console.error('Paystack public key is missing.');
            alert(
              'Payment cannot be processed at this moment. Please contact support.'
            );
            return;
          }

          // Show loading state
          setIsLoading(true);

          const popup = new paystackModule();
          popup.checkout({
            key: publicKey,
            amount, // Amount in smallest currency unit
            email,
            reference,
            metadata,
            currency: 'NGN', // Add currency (NGN, USD, etc.)
            onSuccess: (transaction: any) => {
              router.push(transaction?.redirecturl);
            },
            onLoad: () => {
              setIsLoading(false);
            },
            onCancel: () => {
              alert(
                "Payment was cancelled. Please try again when you're ready."
              );
              setIsLoading(false);
            },
            onError: error => {
              console.error('Payment error:', error.message);
              setIsLoading(false);
              // Handle error (e.g., show error message)
              alert(
                `Payment failed: ${error.message}. Please try again or contact support.`
              );
            },
          });
        } catch (error) {
          console.error('Error initiating payment:', error);
          setIsLoading(false);
          alert('An unexpected error occurred. Please try again later.');
        }
      }

      if (selectedPayment === 'centiiv') {
        const amount = checkoutTotal;
        const note = `Payment for Order ${orderId} by Buyer ${loggedInUser.firstName}`;

        const email = loggedInUser.email;
        const reference = transactionRef;

        const metadata = {
          transactionId,
          buyerId: loggedInUser.id,
          orderId,
          email,
          reference,
        };

        try {
          const initializeCentiivPay = async () => {
            const headers = {
              accept: 'application/json',
              'content-type': 'application/json',
              authorization: `Bearer ${process.env.NEXT_PUBLIC_CENTIIV_TEST_APIKEY}`,
            };

            const response = await axios.post(
              'https://api.centiiv.com/api/v1/direct-pay',
              {
                amount,
                note,
                metadata,
              },
              {
                headers: headers,
              }
            );

            if (response.data.success && response.data.data.link) {
              setCplId(response.data.data.id);
              setCpoId(orderId);
              setCptId(transactionId);

              toast.success(response.data.message);

              setTimeout(() => {
                router.push(
                  `${response.data.data.link}&callback_url=${window.location.origin}/buyers/checkout`
                );
              }, 3000);
            } else {
              alert('Failed to initialize CentiivPay. Please try again.');
              setIsLoading(false);
            }
          };

          initializeCentiivPay();
        } catch (error) {
          // console.error('Error preparing order:', error);
          alert(
            'An error occurred while preparing your order. Please try again.'
          );
        } finally {
          setIsLoading(false); // Hide loading state after mutation is triggered
        }
      }
    }
  }, [orderId, transactionId, loggedInUser?.email]);

  if (isLoading) {
    return (
      <div className="w-full flex flex-col gap-6 py-0 ">
        <h1 className="text-xl font-bold">Checkout</h1>{' '}
        <div className="w-full my-16 flex items-center justify-center">
          <InlineLoader color="eweko_green" size="100" textSize="30" />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col gap-6 py-0 ">
      {trxref && reference && <SuccessModal />}
      {type && payment_method && id && status && <SuccessModal />}
      <h1 className="text-xl font-bold">Checkout</h1>

      {cart && cart.items && cart.items.length > 0 ? (
        <div className="flex flex-col lg2:flex-row flex-wrap gap-8">
          <div className="w-full flex flex-col lg2:flex-row lg2:w-[65%] flex-wrap transition-all duration-300 gap-4">
            <div className="bg-white border border-[#d9d9d9] rounded-[10px] w-full p-6 transition-all duration-300 h-fit">
              {addressStep === 1 && <AddressList />}
              {addressStep === 2 && <AddAddress />}
              {addressStep === 3 && <EditAddress />}
            </div>

            <div className="bg-white border border-[#d9d9d9] rounded-[10px] w-full p-6 transition-all duration-300 h-fit">
              <div
                onClick={() => setIsDeliveryOpen(!isDeliveryOpen)}
                className="flex gap-3"
              >
                <span
                  className={`w-[21px] h-[21px] rounded-full flex items-center justify-center ${selectedDelivery ? 'bg-eweko_green_light' : 'bg-eweko_green_dark/60'}`}
                >
                  <IoIosCheckmark size={21} className="text-white" />
                </span>

                <span className="font-[600] text-eweko_green_dark">2.</span>
                <span className="font-[600] text-eweko_green_dark">
                  Delivery Details
                </span>
              </div>

              {selectedAddress && isDeliveryOpen && (
                <div className="w-full flex flex-col gap-6 items-start mt-6">
                  <div className="w-full flex flex-col gap-6 -mt-2">
                    <div className="flex flex-col lg2:flex-row items-start justify-between border border-[#d9d9d9] p-6 text-eweko_green_dark bg-[#f5f5f5] rounded-[7px] w-full">
                      <div className="w-full lg2:w-[60%] xl:w-[65%]">
                        <RadioGroup
                          value={selectedDelivery ? selectedDelivery : ''}
                          onValueChange={handleChange}
                          className="w-full space-y-4"
                        >
                          <div className="flex gap-3 items-start w-[90%]">
                            <RadioGroupItem value="pickup" id="pickup" />
                            <Label
                              className="text-[16px] text-eweko_green_dark -mt-[2px] flex flex-col gap-2"
                              htmlFor="pickup"
                            >
                              <h3 className="font-bold text-[17px]">
                                Pickup Delivery
                              </h3>
                              <p>126B, Aggregates Road, Eweko, Epe</p>
                              <p>0700 Eweko</p>
                            </Label>
                          </div>
                          <div className="flex gap-3 items-start w-[90%]">
                            <RadioGroupItem value="doorstep" id="doorstep" />
                            <Label
                              className="text-[16px] text-eweko_green_dark -mt-[2px] flex flex-col gap-2"
                              htmlFor="doorstep"
                            >
                              <h3 className="font-bold text-[17px]">
                                Doorstep Delivery
                              </h3>
                              <p>{`${selectedAddress?.houseNumber}, ${selectedAddress?.streetName}, ${selectedAddress?.community}, ${selectedAddress?.lga}, ${selectedAddress?.state}`}</p>
                              <p>{loggedInUser?.phone}</p>
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="bg-white border border-[#d9d9d9] rounded-[10px] w-full p-6 transition-all duration-300 h-fit">
              <div
                onClick={() => setIsPaymentOpen(!isPaymentOpen)}
                className="flex gap-3"
              >
                <span
                  className={`w-[21px] h-[21px] rounded-full flex items-center justify-center ${selectedPayment ? 'bg-eweko_green_light' : 'bg-eweko_green_dark/60'}`}
                >
                  <IoIosCheckmark size={21} className="text-white" />
                </span>

                <span className="font-[600] text-eweko_green_dark">3.</span>
                <span className="font-[600] text-eweko_green_dark">
                  Payment Method
                </span>
              </div>

              {selectedAddress && selectedDelivery && isPaymentOpen && (
                <div className="flex flex-col lg2:flex-row items-start justify-between border border-[#d9d9d9] p-6 text-eweko_green_dark bg-[#f5f5f5] rounded-[7px] w-full mt-3">
                  <RadioGroup
                    value={selectedPayment ? selectedPayment : ''}
                    onValueChange={handlePaymentSelect}
                    className="w-full space-y-4"
                  >
                    <div className="flex gap-3 items-start w-full">
                      <RadioGroupItem value="centiiv" id="centiiv" />
                      <Label
                        className="text-[16px] text-eweko_green_dark -mt-[2px] flex items-center justify-between gap-2 w-full"
                        htmlFor="centiiv"
                      >
                        <div className="flex flex-col gap-1">
                          <h3 className="font-bold text-[17px]">
                            Pay With Centiiv (1% fees)
                          </h3>
                          <p className="text-[14px]">
                            Pay N{((1 / 100) * checkoutTotal).toLocaleString()}{' '}
                            with Bank Transfer
                          </p>
                        </div>

                        <Image
                          src={centiiv}
                          alt="Centiiv Escrow"
                          width={60}
                          height={14}
                          className="object-contain"
                        />
                      </Label>
                    </div>
                    <div className="flex gap-3 items-start w-full">
                      <RadioGroupItem value="paystack" id="paystack" />
                      <Label
                        className="text-[16px] text-eweko_green_dark -mt-[2px] flex items-center justify-between gap-2 w-full"
                        htmlFor="paystack"
                      >
                        <div className="flex flex-col gap-1">
                          <h3 className="font-bold text-[17px]">
                            Pay With Paystack (1.5% fees)
                          </h3>
                          <p className="text-[14px]">
                            Pay N{(1.5 / 100) * checkoutTotal} with Bank
                            Transfer
                          </p>
                        </div>
                        <Image
                          src={paystack}
                          alt="Paystack Payment"
                          width={103}
                          height={17}
                          className="object-contain"
                        />
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              )}
            </div>
          </div>

          <div className="w-full lg2:w-[28%] h-fit">
            <div className="bg-white border border-[#d9d9d9] rounded-[10px] p-6 w-full h-fit flex flex-col gap-8">
              <h1 className="text-eweko_green_dark text-[18px]">
                Order Summary
              </h1>

              <div className="w-full flex flex-col gap-6">
                <div className="w-full flex justify-between items-center">
                  <p>Items Total ({cart.items.length})</p>{' '}
                  <p className="font-bold">N{cartSubTotal.toLocaleString()}</p>
                </div>
                <div className="w-full flex justify-between items-center">
                  <p>Shipping Fee</p>{' '}
                  <p className="font-bold">N{shipping.toLocaleString()}</p>
                </div>
                <div className="w-full flex justify-between items-center">
                  <p>VAT</p> <p className="font-bold">7.5%</p>
                </div>
                <div className="w-full flex justify-between items-center">
                  <p>Checkout Total</p>{' '}
                  <p className="font-bold">N{checkoutTotal.toLocaleString()}</p>
                </div>

                <Dialog>
                  <DialogTrigger asChild disabled={!isCheckoutReady}>
                    <Button
                      type="button"
                      disabled={!isCheckoutReady}
                      className="rounded-[10px] px-8 py-5 text-lg duration-500 transition-all bg-eweko_green_dark text-white hover:bg-black hover:font-bold leading-none w-full text-center"
                    >
                      Confirm Order
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-full overflow-y-auto max-h-[90%] min-w-[40%]">
                    <DialogHeader>
                      <DialogTitle className="text-eweko_green text-xl">
                        Confirm Your Order
                      </DialogTitle>
                      <DialogDescription className="text-eweko_green_dark">
                        Confirm that everything is correct and proceed to
                        payment
                      </DialogDescription>
                    </DialogHeader>
                    <div className="flex flex-col items-center justify-center gap-6">
                      <div className="flex flex-col gap-3 w-full">
                        <h3 className="font-bold text-[17px]">
                          Customer Details
                        </h3>
                        <div className="w-full flex justify-between items-center">
                          <p className="">Name:</p>
                          <p className="font-bold">
                            {`${capitalizeFirstLetter(loggedInUser?.firstName)} ${capitalizeFirstLetter(loggedInUser?.lastName)}`}
                          </p>
                        </div>
                        <div className="w-full flex justify-between items-center">
                          <p className="">Phone Number:</p>
                          <p className="font-bold">{loggedInUser?.phone}</p>
                        </div>
                        <div className="w-full flex justify-between items-center">
                          <p className="">Address:</p>
                          <p className="font-bold text-right">
                            <span>
                              {`${selectedAddress && selectedAddress.houseNumber}`}
                              ,
                            </span>{' '}
                            <span>{`${selectedAddress && selectedAddress.streetName},`}</span>
                            <br />{' '}
                            {`${selectedAddress && selectedAddress.community}`}
                            <br />{' '}
                            <span>{`${selectedAddress && selectedAddress.lga}, ${selectedAddress && selectedAddress.state},`}</span>
                          </p>
                        </div>
                      </div>
                      <Separator className="my-4" />
                      <div className="flex flex-col gap-3 w-full">
                        <h3 className="font-bold text-[17px]">Order Details</h3>
                        <div className="flex flex-col gap-4 w-full">
                          {cart && cart.items.length > 0 && (
                            <div className="flex flex-col gap-3">
                              {cart.items.map((item, i) => (
                                <div
                                  key={i}
                                  className="flex items-center justify-between"
                                >
                                  <div className="flex gap-4 items-center">
                                    <span>
                                      <Image
                                        src={item.produce.images[0]}
                                        alt={item.produce.name}
                                        width={80}
                                        height={80}
                                        className="w-[80px] h-[60px] object-cover rounded-lg"
                                      />
                                    </span>
                                    <div className="flex flex-col gap-1">
                                      <p className="text-eweko_green_dark text-[16px]">
                                        {item.produce.name}
                                      </p>
                                      <p className="text-[#6b6b6b] text-[12px]">
                                        Kg(s): {item.quantity}
                                      </p>
                                    </div>
                                  </div>

                                  <p className="font-bold">
                                    N
                                    {(
                                      item.quantity * item.produce.price
                                    ).toLocaleString()}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                          <div className="w-full flex justify-between items-center">
                            <p>Shipping Fee</p>{' '}
                            <p className="font-bold">
                              N{shipping.toLocaleString()}
                            </p>
                          </div>
                          <div className="w-full flex justify-between items-center">
                            <p>VAT</p> <p className="font-bold">7.5%</p>
                          </div>
                          <div className="w-full flex justify-between items-center">
                            <p>Total Amount</p>{' '}
                            <p className="font-bold">
                              N{checkoutTotal.toLocaleString()}
                            </p>
                          </div>
                        </div>
                      </div>
                      <Separator className="my-4" />
                      <div className="flex flex-col gap-3 w-full">
                        <h3 className="font-bold text-[17px]">
                          Delivery Details
                        </h3>
                        <div className="w-full flex justify-between items-center">
                          <p className="">Method:</p>
                          <p className="font-bold">
                            {capitalizeFirstLetter(
                              selectedDelivery ? selectedDelivery : ''
                            )}
                          </p>
                        </div>
                        <div className="w-full flex justify-between items-center">
                          <p className="">Delivery Address:</p>
                          <p className="font-bold w-[1/2] text-right">
                            {shippingAddress}
                          </p>
                        </div>
                      </div>
                      <Separator className="my-4" />
                      <div className="flex flex-col gap-3 w-full">
                        <h3 className="font-bold text-[17px]">
                          Payment Details
                        </h3>
                        <div className="w-full flex justify-between items-center">
                          <p className="">Method:</p>
                          <p className="font-bold">
                            {selectedPayment &&
                            selectedPayment === 'centiiv' ? (
                              <Image
                                src={centiiv}
                                alt="Centiiv Escrow"
                                width={60}
                                height={14}
                                className="object-contain"
                              />
                            ) : (
                              <Image
                                src={paystack}
                                alt="Paystack Payment"
                                width={103}
                                height={17}
                                className="object-contain"
                              />
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                    <DialogFooter className="justify-center mt-6">
                      <Button
                        type="button"
                        disabled={!isCheckoutReady}
                        onClick={handleCheckout}
                        className="rounded-[10px] px-8 py-6 text-lg duration-500 transition-all bg-eweko_green_dark text-white hover:bg-black hover:font-bold leading-none w-full text-center"
                      >
                        {isLoading ? (
                          <InlineLoader color="white" size="30" textSize="10" />
                        ) : (
                          'Proceed to Payment'
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-eweko_red flex flex-col items-center justify-center font-bold mt-12 gap-6">
          <p>Your Shopping Cart is empty!</p>
          <Link
            href="/buyers/produce"
            className="rounded-[10px] px-8 py-4 text-lg duration-500 transition-all bg-eweko_green text-white hover:bg-black hover:font-bold leading-none w-fit"
          >
            Visit Produce
          </Link>
        </div>
      )}
    </div>
  );
};

const AddressList: FC = () => {
  const {
    setAddress2edit,
    setAddressStep,
    setSelectedAddress,
    selectedAddress,
    setIsDeliveryOpen,
    setIsAddressOpen,
    isAddressOpen,
    loggedInUser,
    addresses,
    setAddresses,
  } = useGlobalState();

  const [selectedAddressId, setSelectedAddressId] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);

  const { refetch } = useSafeQuery<any>(
    ['addresses'],
    `${AllRoutes.addresses}?userId=${loggedInUser?.id}`,
    {
      enabled: true,
      refetchOnWindowFocus: true,
    }
  );

  // Fetch user data
  const { data: userData } = useSafeQuery<any>(
    ['user'],
    `${AllRoutes.users}/buyers/${loggedInUser?.id}`,
    {
      enabled: true,
      refetchOnWindowFocus: true,
    }
  );

  useEffect(() => {
    if (userData) {
      setUser(userData);
    }
  }, [userData]);

  useEffect(() => {
    const fetchAddresses = async () => {
      const { data, isError, error, isSuccess } = await refetch();

      if (isError) {
      }

      if (isSuccess && data) {
        setAddresses(data);
        setIsLoading(false);
      }
    };

    fetchAddresses();
  }, []);

  const handleChange = (value: string) => {
    setSelectedAddressId(value);

    if (addresses) {
      const addr = addresses.filter(addr => addr.id === value)[0];
      setSelectedAddress(addr);
      setIsAddressOpen(false);
      setIsDeliveryOpen(true);
    }
  };

  if (isLoading || !addresses) {
    return (
      <div className="w-full flex flex-col gap-6 py-0 ">
        <div className="w-full my-16 flex items-center justify-center">
          <InlineLoader color="eweko_green" size="60" textSize="20" />
        </div>
      </div>
    );
  }

  return (
    <div className="transition-all duration-300 flex flex-col gap-3">
      <div
        onClick={() => setIsAddressOpen(!isAddressOpen)}
        className="flex gap-3"
      >
        <span
          className={`w-[21px] h-[21px] rounded-full flex items-center justify-center ${selectedAddress ? 'bg-eweko_green_light' : 'bg-eweko_green_dark/60'}`}
        >
          <IoIosCheckmark size={21} className="text-white" />
        </span>

        <span className="font-[600] text-eweko_green_dark">1.</span>
        <span className="font-[600] text-eweko_green_dark">
          Delivery Address
        </span>
      </div>
      {isAddressOpen && (
        <div className="">
          {addresses && addresses.length > 0 ? (
            <div className="w-full flex flex-col gap-6 items-start">
              <h3 className="text-[#626262]">
                Address Book ({addresses.length})
              </h3>
              <div className="w-full flex flex-col gap-6 -mt-2">
                <RadioGroup
                  value={selectedAddressId}
                  onValueChange={handleChange}
                  className="w-full space-y-4"
                >
                  {addresses.map((address: Address) => (
                    <div
                      key={address.id}
                      className="flex items-start justify-between border border-[#d9d9d9] p-6 text-eweko_green_dark bg-[#f5f5f5] rounded-[7px]"
                    >
                      <div className="flex gap-3 items-start w-[90%]">
                        <RadioGroupItem
                          value={address ? address.id : '1'}
                          id={address.id}
                        />
                        <Label
                          className="text-[16px] text-eweko_green_dark -mt-[4px] flex flex-col gap-2"
                          htmlFor={address.id}
                        >
                          <h3 className="font-bold text-[17px]">
                            {`${capitalizeFirstLetter(loggedInUser?.firstName)} ${capitalizeFirstLetter(loggedInUser?.lastName)}`}
                          </h3>
                          <p>{`${address.houseNumber}, ${address.streetName}, ${address.community}, ${address.lga}, ${address.state}`}</p>
                          <p className="text-[15px]">
                            {user && user?.contact.primaryPhone}
                          </p>
                        </Label>
                      </div>

                      <div
                        className="text-[13px] text-eweko_green_dark flex gap-2 items-center cursor-pointer"
                        onClick={() => {
                          setAddress2edit(address.id);
                          setAddressStep(3);
                        }}
                      >
                        <span>EDIT</span>
                        <HiPencil />
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>
              <Button
                variant="link"
                className="px-0 hover:no-underline text-eweko_green_light font-[500] hover:text-eweko_green_dark"
                onClick={() => setAddressStep(2)}
              >
                <FaPlus /> Add Address
              </Button>
            </div>
          ) : (
            <div className="w-full flex flex-col gap-6">
              <h3 className="text-[#626262]">
                Address Book ({addresses && addresses.length})
              </h3>

              <div className="w-full flex flex-col gap-6 items-center justify-center text-[#626262]">
                <p>No address added to your account</p>
                <Button
                  variant="link"
                  className="hover:no-underline text-eweko_green_light font-[500] hover:text-eweko_green_dark"
                  onClick={() => setAddressStep(2)}
                >
                  <FaPlus /> Add Address
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const AddAddress: FC = () => {
  const { setAddressStep } = useGlobalState();
  const [selectedAddress, setSelectedAddress] = useState(false);
  const [isAddressOpen, setIsAddressOpen] = useState(true);

  return (
    <div className="transition-all duration-300 flex flex-col gap-3">
      <div
        onClick={() => setIsAddressOpen(!isAddressOpen)}
        className="flex gap-3"
      >
        <span
          className={`w-[21px] h-[21px] rounded-full flex items-center justify-center ${selectedAddress ? 'bg-eweko_green_light' : 'bg-eweko_green_dark/60'}`}
        >
          <IoIosCheckmark size={21} className="text-white" />
        </span>

        <span className="font-[600] text-eweko_green_dark">1.</span>
        <span className="font-[600] text-eweko_green_dark">
          Delivery Address
        </span>
      </div>

      {isAddressOpen && (
        <div className="w-full flex flex-col gap-6">
          <div className="flex gap-4 items-center">
            <IoReturnUpBack
              onClick={() => setAddressStep(1)}
              size={20}
              className={`text-eweko_green_dark`}
            />
            <h3 className="text-[#626262]">Add Address</h3>
          </div>

          <AddAddressForm />
        </div>
      )}
    </div>
  );
};

const EditAddress = () => {
  const { setAddressStep, address2edit, selectedAddress, addresses } =
    useGlobalState();
  const [address2beEdited, setAddress2beEdited] = useState<Address | null>(
    null
  );
  const [isAddressOpen, setIsAddressOpen] = useState(true);

  useEffect(() => {
    if (addresses) {
      const filteredAddress = addresses.filter(
        address => address.id === address2edit
      )[0];

      setAddress2beEdited(filteredAddress);
    }
  }, []);

  return (
    <div className="transition-all duration-300 flex flex-col gap-3">
      <div
        onClick={() => setIsAddressOpen(!isAddressOpen)}
        className="flex gap-3"
      >
        <span
          className={`w-[21px] h-[21px] rounded-full flex items-center justify-center ${selectedAddress ? 'bg-eweko_green_light' : 'bg-eweko_green_dark/60'}`}
        >
          <IoIosCheckmark size={21} className="text-white" />
        </span>

        <span className="font-[600] text-eweko_green_dark">1.</span>
        <span className="font-[600] text-eweko_green_dark">
          Delivery Address
        </span>
      </div>

      {isAddressOpen && (
        <div className="w-full flex flex-col gap-6">
          <div className="flex gap-4 items-center">
            <IoReturnUpBack
              onClick={() => setAddressStep(1)}
              size={20}
              className={`text-eweko_green_dark`}
            />
            <h3 className="text-[#626262]">Edit Address</h3>
          </div>

          <EditAddressForm address={address2beEdited} />
        </div>
      )}
    </div>
  );
};

export default function SuccessModal() {
  const [visible, setVisible] = useState(true);
  const router = useRouter();
  const params = useSearchParams();
  const reference = params.get('trxref');
  const type = params.get('type');
  const payment_method = params.get('payment_method');
  const id = params.get('id');
  const status = params.get('status');

  const {
    selectedDelivery,
    selectedPayment,
    setSelectedAddress,
    setSelectedDelivery,
    setSelectedPayment,
    setAddress2edit,
    setCart,
  } = useGlobalState();
  const { mutate } = useSafeMutation<Cart, Error>(AllRoutes.cart, 'delete', {});

  useEffect(() => {
    mutate();
  }, []);

  useEffect(() => {
    const redirectTimer = setTimeout(() => {
      setSelectedAddress(null);
      setSelectedDelivery(null);
      setSelectedPayment(null);
      setAddress2edit(null);
      setCart(null);
      router.push(`/buyers/transactions`);
    }, 5000);
    const closeTimer = setTimeout(() => setVisible(false), 5000);
    return () => {
      clearTimeout(redirectTimer);
      clearTimeout(closeTimer);
    };
  }, [type, id, payment_method, status, router]);

  if (!visible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-700 bg-opacity-50 z-50">
      <div className="bg-white p-8 rounded-lg shadow-md text-center w-[80%] sm2:w-[60%] md:w-[50%] md3:w-[35%] text-eweko_green_dark">
        <div className="w-full flex justify-center mb-4">
          <div className="bg-eweko_green/20 rounded-full h-[116px] w-[116px] flex items-center justify-center">
            <FaCheck className="text-[60px] text-eweko_green" />
          </div>
        </div>
        <h1 className="text-2xl font-bold mb-6">Thank you!</h1>
        <div>
          <p className="">Your order {reference || id} has been placed.</p>
          <p className="">
            We have sent an email to you with your order confirmation and
            receipt.
          </p>
        </div>

        <div className="flex flex-col gap-3 mt-6">
          <div className="flex items-center gap-6">
            <div className="bg-eweko_green/20 rounded-lg h-[50px] w-[60px] flex items-center justify-center">
              <FaTruck className="text-lg text-eweko_green" />
            </div>
            <div className="flex flex-col items-start">
              <h3 className="text-[18px]">Delivery Method</h3>
              <p>{selectedDelivery === 'doorstep' ? 'Doorstep' : 'Pickup'}</p>
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="bg-eweko_green/20 rounded-lg h-[50px] w-[60px] flex items-center justify-center">
              <FaRegCreditCard className="text-lg text-eweko_green" />
            </div>
            <div className="flex flex-col items-start">
              <h3 className="text-[18px]">Payment Method</h3>
              <p>{selectedPayment === 'paystack' ? 'Paystack' : 'Centiiv'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
