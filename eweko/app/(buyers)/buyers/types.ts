import { StaticImageData } from 'next/image';
import { ReactNode } from 'react';

export type ShoppingCartItem = {
  userId: string;
  produceId: string;
  name: string;
  category: string;
  price: number;
  discountedPrice: number | null;
  quantity: number;
  minOrderQty: number;
  qtyAvailable: number;
  subtotal: number;
  imageUrl: StaticImageData | string;
};

// export type Produce = {
//   userId: string;
//   produceId: string;
//   name: string;
//   category: string;
//   price: number;
//   discountedPrice: number | null;
//   quantity: number;
//   minOrderQty: number;
//   qtyAvailable: number;
//   subtotal: number;
//   totalprice: number;
//   imageUrl: StaticImageData | string;
// };

export interface AddAddress {
  userId: string;
  houseNumber: string;
  streetName: string;
  lga: string;
  state: string;
  country: string;
  isDefault: boolean;
}

export interface Address {
  id: string;
  userId: string;
  houseNumber: string;
  streetName: string;
  community: string;
  lga: string;
  state: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface Produce {
  id: string;
  farmerId: string;
  categoryId: string;
  name: string;
  description: string;
  price: number;
  negotiablePrice: number;
  stock: number;
  minOrderQty: number;
  harvestDate: string;
  images: string[];
  slug: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface CartItem {
  id: string;
  produce: Produce;
  quantity: number;
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  totalCost: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface OrderItem {
  produce: string; // ID of the produce item
  quantity: number;
  totalPrice: number;
}

export interface CreateOrder {
  userId: string;
  items: OrderItem[];
  totalCost: number;
  shippingFee: number;
  paymentMethod: string;
  shippingAddress: string;
  finalTotalCost: number;
}

export interface CreateTransaction {
  user: string;
  order: string;
  invoice?: string; // Optional field
  totalAmount: number;
  paymentMethod: string;
  paymentType: string;
}

export interface Transaction {
  id: string;
  user: string;
  order: string;
  totalAmount: number;
  status: 'Pending' | 'Completed' | 'Failed'; // Define possible statuses
  paymentMethod: string;
  paymentType: string;
  reference: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface OrderItem2 {
  produce: string;
  quantity: number;
  totalPrice: number;
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem2[];
  totalCost: number;
  shippingFee: number;
  status: 'Processing' | 'Completed' | 'Cancelled' | 'Shipped'; // Define possible statuses
  paymentMethod: string;
  paymentStatus: 'Pending' | 'Paid' | 'Failed'; // Define possible payment statuses
  shippingAddress: string;
  shippedDate: string | null;
  finalTotalCost: number;
  paymentDate: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface TransactionResponse {
  reference: string;
  trans: string;
  status: string;
  message: string;
  transaction: string;
  trxref: string;
  return: string; // JSON string that needs parsing
  redirecturl: string;
}

export interface IFarmer {
  id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  userType: string;
  twoFA: boolean;
  verified: boolean;
  isPhoneVerified: boolean;
  isActive: boolean;
  lastLogin: Date;
  notifications: string[];
  addresses: string[];
  farmName: string;
  produces: string[];
  __v: number;
}

export interface ICategory {
  id: string;
  name: string;
  produces: string[];
  slug: string;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface IProduce {
  id: string;
  farmer: IFarmer;
  category: ICategory;
  name: string;
  description: string;
  price: number;
  negotiablePrice: number;
  stock: number;
  minOrderQty: number;
  harvestDate: Date;
  images: string[];
  slug: string;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface FarmerStatItem {
  label: string;
  value: string | number;
}

export type ProduceTableRow = {
  sn: number;
  id: string;
  image: string;
  produce: string;
  availableQty: number;
  pricePerKg: number;
  harvestDate: string;
};

export interface ITransaction {
  id: string;
  user: string;
  order: string;
  totalAmount: number;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  paymentType: PaymentType;
  reference: string;
  processed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum PaymentStatus {
  PENDING = 'Pending',
  COMPLETED = 'Completed',
  REFUNDED = 'Refunded',
  FAILED = 'Failed',
}

export enum PaymentMethod {
  CENTIIVPAY = 'CentiivPay',
  PAYSTACK = 'Paystack',
}

export enum PaymentType {
  PURCHASE = 'Purchase',
  CONTRACT_FARMING = 'Contract Farming',
}

export type TransactionTableRow = {
  sn: number;
  id: string;
  status: string;
  date: string;
  method: string;
  reference: string;
  orderValue: number;
};

export interface IJwtPayload {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  userType: 'BUYER' | 'FARMER' | 'ADMIN' | 'AGENT' | 'LOGISTICS';
  profilePicture: string;
  iat: number;
}
