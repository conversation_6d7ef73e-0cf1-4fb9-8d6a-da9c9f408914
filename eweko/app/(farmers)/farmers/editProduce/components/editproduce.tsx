'use client';
import { uploadImageToCloudinary } from '@/app/utils/cloudinary';
import { useSafeMutation } from '@/axios/query-client';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useGlobalState } from '@/globalStore';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { Button } from '../../../../../components/ui/button';
import crypto from 'crypto';

interface Category {
  id: string;
  name: string;
}

export default function EditProduce() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const productId = searchParams.get('productId');

  const [productName, setProductName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [price, setPrice] = useState<number | undefined>(undefined);
  const [negotiablePrice, setNegotiablePrice] = useState<number | undefined>(
    undefined
  );
  const [stock, setStock] = useState<number | undefined>(undefined);
  const [minOrderQuantity, setMinOrderQuantity] = useState<number | undefined>(
    undefined
  );
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [uploadedImageURLs, setUploadedImageURLs] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState('');
  const [uploading, setUploading] = useState(false);
  const [user, setUser] = useState(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const { userLoginId, setProduceUpdated } = useGlobalState();
  const [errorMessage, setErrorMessage] = useState('');
  const queryClient = useQueryClient();

  // Fetch produce data for editing
  const {
    data: produce,
    isLoading: isProduceLoading,
    isError: isProduceError,
    error: produceError,
  } = useQuery({
    queryKey: ['produce', productId],
    queryFn: async () => {
      if (!productId) {
        throw new Error('Product ID is missing.');
      }
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_APIURL}/produce/${productId}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch produce');
      }
      return await response.json();
    },
    enabled: !!productId,
  });

  // Fetch categories using React Query with the Object form
  const {
    data: categories = [],
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['category'],
    queryFn: async () => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_APIURL}/category`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      const data = await response.json();
      return data;
    },
  });

  useEffect(() => {
    if (isError) {
      console.error('Error fetching categories:', isError);
    }
  }, []);

  interface CategoryChangeHandler {
    (value: string): void;
  }

  const handleCategoryChange: CategoryChangeHandler = value => {
    setSelectedCategoryId(value);
  };

  useEffect(() => {
    if (produce && !isProduceLoading) {
      setProductName(produce.name || '');
      setDescription(produce.description || '');
      setSelectedDate(new Date(produce.harvestDate));
      setSelectedCategoryId(produce.categoryId || '');
      setSelectedType(produce.type || '');
      setPrice(produce.price || undefined);
      setNegotiablePrice(produce.negotiablePrice || undefined);
      setStock(produce.stock || undefined);
      setMinOrderQuantity(produce.minOrderQty || undefined);
      setUploadedImageURLs(
        Array.isArray(produce.images) ? produce.images : [produce.images]
      );
    }
  }, [produce, isProduceLoading]);

  const generateSignature = (
    params: Record<string, any>,
    apiSecret: string
  ): string => {
    const queryString = Object.keys(params)
      .sort()
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    return crypto
      .createHmac('sha1', apiSecret)
      .update(queryString)
      .digest('hex');
  };

  //function to delete cloudinary

  const deleteImageFromCloudinary = async (imageUrl: string): Promise<void> => {
    // Extract the public ID from the Cloudinary URL
    const publicId = imageUrl.split('/').slice(-2, -1)[0]; // Adjust this based on your Cloudinary URL structure
    const ApiKey = process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY;
    try {
      const timestamp = Math.floor(Date.now() / 1000); // Current timestamp
      const params = {
        publicid: publicId,
        timestamp,
        api_key: ApiKey,
      };
      // Generate the signature

      const signature = generateSignature(
        params,
        'Sg0VGdLSJXXtxm4Da_3uHB2gt5k'
      );
      const paramsWithSignature = { ...params, signature };

      const response = await fetch(
        `https://api.cloudinary.com/v1_1/do1iiylud/image/destroy`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            public_id: publicId,
            upload_preset: 'your_preset',
            params,
          }),
        }
      );
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`cannot to delete image: ${JSON.stringify(errorData)}`);
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  };

  // delete image from cloudinary

  const handleRemoveImage = async (index: number) => {
    try {
      const imageUrl = uploadedImageURLs[index];
      if (!imageUrl) return;

      // Delete the image from Cloudinary
      await deleteImageFromCloudinary(imageUrl);

      // Remove the image from the local state
      setUploadedImageURLs(prev => prev.filter((_, i) => i !== index));
    } catch (error) {
      console.error('Error deleting image:', error);
    }
  };

  const handleImageUpload = async (file: File) => {
    if (!file) return;
    try {
      const imageUrl = await uploadImageToCloudinary(file);
      setImagePreview(imageUrl); // Set the preview to the Cloudinary URL
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setUploading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    if (files.length > 3) {
      setErrorMessage('You can only select up to 3 images.');
      return;
    }
    const selectedFiles = Array.from(files);
    setSelectedImages(selectedFiles);
  };

  const handleReplaceImage = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.target.files && e.target.files[0]) {
      const updatedImages = [...selectedImages];
      updatedImages[index] = e.target.files[0]; // Replace the image at the given index
      setSelectedImages(updatedImages);
    }
  };

  const { mutateAsync, isPending } = useSafeMutation<
    any,
    Error,
    {
      id: string;
      name: string;
      description: string;
      harvestDate: Date | undefined;
      categoryId: string | undefined;
      type: string;
      price: number | undefined;
      negotiablePrice: number | undefined;
      stock: number | undefined;
      minOrderQty: number | undefined;
      images: string[];
    }
  >(`${process.env.NEXT_PUBLIC_APIURL}/produce/${productId}`, 'patch', {
    onSuccess: data => {
      toast.success('Product updated successfully!', {
        position: 'top-center',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: false,
        pauseOnHover: true,
        draggable: true,
        theme: 'light',
      });
      setProduceUpdated(true);

      setTimeout(() => {
        router.push('/farmers/produce');
      }, 3000);
    },
    onError: (error: any) => {
      console.error('Error submitting form:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to update product.',
        {
          position: 'top-center',
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          theme: 'light',
        }
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    if (selectedImages.length === 0 && uploadedImageURLs.length === 0) {
      setErrorMessage('Please select at least one image.');
      return;
    }
    try {
      setUploading(true);
      const imageUploadPromises = selectedImages.map(file =>
        uploadImageToCloudinary(file)
      );
      const uploadedURLs = await Promise.all(imageUploadPromises);
      const formData = {
        id: productId!,
        farmerId: userLoginId,
        name: productName,
        description,
        harvestDate: selectedDate,
        categoryId: selectedCategoryId,
        type: selectedType,
        price,
        negotiablePrice,
        stock,
        minOrderQty: minOrderQuantity,
        images: [...uploadedImageURLs, ...uploadedURLs],
      };
      await mutateAsync(formData);
    } catch (error: any) {
      console.error('Error submitting form:', error);
      setErrorMessage(
        error?.response?.data?.message ||
          'Failed to update product. Please try again.'
      );
    } finally {
      setUploading(false);
    }
  };

  if (!productId) {
    return <div>Product ID is missing. Please provide a valid Product ID.</div>;
  }

  if (isProduceError) {
    console.error('Error fetching produce:', produceError);
    return (
      <div>
        Error loading produce data. Please try again.{' '}
        {produceError && <pre>{JSON.stringify(produceError, null, 2)}</pre>}
      </div>
    );
  }

  return (
    <div className="md:px-10 pb-5 text-eweko_green_dark ">
      {/* Success Message */}
      {successMessage && (
        <div className="bg-eweko_green_light mt-[-20px] mx-[-40px] text-center text-white p-4 mb-4">
          {successMessage}
        </div>
      )}
      {/* Error Message */}
      {errorMessage && (
        <div className="bg-red-300 text-center mt-[-20px] mx-[-40px] text-white p-4 mb-4">
          {errorMessage}
        </div>
      )}
      <Button
        onClick={() => router.back()}
        className="cursor-pointer flex items-center text-eweko_green_light hover:text-white hover:bg-eweko_green_light bg-transparent transition-all duration-300 p-0 hover:px-4"
        variant="ghost"
      >
        <span className="mr-1 text-lg">&lt; </span> Back
      </Button>
      <h1 className="text-[min(10vw,19px)] font-bold mb-8">Edit Produce</h1>
      <div className="flex flex-col lg:flex-row gap-8">
        <div className="bg-white border w-full lg:w-[50%] xl:w-[50%] h-full rounded-lg">
          <div className="">
            <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
              Produce Image
            </p>
            <div className="flex flex-col h-[70%] p-4 items-center">
              <label
                htmlFor="image-upload"
                className="border-2 border-dashed border-eweko_green_light bg-gray-100 rounded-lg w-full h-[250px] flex flex-col justify-center items-center cursor-pointer hover:bg-green-50 transition"
              >
                {selectedImages.length > 0 && (
                  <div className="relative w-full h-full">
                    <img
                      src={URL.createObjectURL(selectedImages[3])}
                      alt="Uploaded preview"
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 flex flex-col items-center justify-center gap-4 bg-black/20 rounded-lg">
                      <button
                        onClick={() =>
                          document.getElementById('replaceInput')?.click()
                        } // Trigger replace input
                        className="px-6 py-2 text-sm text-eweko_green_light bg-white rounded-lg hover:bg-gray-100"
                      >
                        Replace
                      </button>
                      <button
                        onClick={() => setSelectedImages([])} // Clear all selected images
                        className="px-6 py-2 text-sm text-red-600 bg-white rounded-lg hover:bg-gray-100"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                )}
                {selectedImages.length === 0 &&
                  uploadedImageURLs.length > 0 && (
                    <div className="relative w-full h-full">
                      <img
                        src={uploadedImageURLs[0]}
                        alt="Existing preview"
                        className="w-full h-full object-cover rounded-lg"
                      />
                      <div className="absolute inset-0 flex flex-col items-center justify-center gap-4 bg-black/20 rounded-lg">
                        <button
                          onClick={() =>
                            document.getElementById('replaceInput')?.click()
                          } // Trigger replace input
                          className="px-6 py-2 text-sm text-eweko_green_light bg-white rounded-lg hover:bg-gray-100"
                        >
                          Replace
                        </button>
                        <button
                          onClick={() => handleRemoveImage(0)}
                          className="px-6 py-2 text-sm text-red-600 bg-white rounded-lg hover:bg-gray-100"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  )}
                {selectedImages.length === 0 &&
                  uploadedImageURLs.length === 0 && (
                    <div
                      className="flex flex-row gap-1 items-center cursor-pointer"
                      onClick={() =>
                        document.getElementById('imageInput')?.click()
                      } // Trigger file input
                    >
                      <span className="text-sm text-eweko_green_light">
                        Click to upload
                      </span>
                      <span className="text-xs text-gray-400">
                        or drag and drop
                      </span>
                    </div>
                  )}
              </label>
              {/* Hidden File Input */}
              <input
                id="imageInput"
                type="file"
                accept="image/*"
                className="hidden"
                multiple
                onChange={handleFileChange}
                required
              />
              {/* Hidden Initial File Input */}
              <input
                id="replaceInput"
                type="file"
                accept="image/*"
                className="hidden"
                multiple
                onChange={e => handleReplaceImage(e, 0)}
                required
              />
            </div>

            <div className="flex gap-4 ml-5 mt-4 mb-5">
              {selectedImages.slice(1, 3).map((image, index) => (
                <div key={index} className="relative w-[80px] h-[80px]">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`Remaining preview ${index + 1}`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 bg-black/20 rounded-lg">
                    <button
                      onClick={() =>
                        document
                          .getElementById(`replaceOtherInput-${index + 1}`)
                          ?.click()
                      } // Trigger replace input for the specific image
                      className="px-4 py-1 text-[9px] text-eweko_green_light bg-white rounded-lg hover:bg-gray-100"
                    >
                      Replace
                    </button>
                    <button
                      onClick={() => {
                        setSelectedImages(prev =>
                          prev.filter((_, i) => i !== index + 1)
                        );
                      }}
                      className="px-4 py-1 text-[9px] text-red-600 bg-white rounded-lg hover:bg-gray-100"
                    >
                      Remove
                    </button>
                  </div>
                  <input
                    id={`replaceOtherInput-${index + 1}`}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={e => handleReplaceImage(e, index + 1)} // Pass the index of the image to replace
                    required
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="bg-transparent flex flex-col gap-6 w-full">
          <div className="bg-white border w-full h-[500px] rounded-lg">
            <p className="w-full text-[min(10vw,17px)] text-left p-5 px-8 border-b">
              General Information
            </p>
            <div className="px-8 py-5 w-full">
              <label className="block text-[min(10vw,15px)] text-gray-600">
                Produce Name
              </label>
              <input
                type="text"
                name="name"
                value={productName}
                onChange={e => setProductName(e.target.value)}
                className="w-full xl:w-[80%] px-4 py-2 border rounded bg-white text-[min(10vw,14px)] mt-1"
                required
              />
              <label className="block text-[min(10vw,15px)] mt-10 text-gray-600">
                Produce Description
              </label>
              <textarea
                name="description"
                value={description}
                onChange={e => setDescription(e.target.value)}
                className="w-full xl:w-[80%] h-[250px] px-4 py-2 border rounded bg-white text-[min(10vw,14px)] mt-1"
                required
              />
            </div>
          </div>
          <div className="bg-white border w-full h-auto rounded-lg">
            <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
              Category
            </p>
            <div className="px-8 py-5 gap-4 flex sm3:flex-row flex-col w-full xl:w-[80%]">
              <div className="w-full">
                <label className="block text-[min(10vw,15px)] font-medium"></label>
                <Select onValueChange={handleCategoryChange}>
                  <SelectTrigger className="w-full text-[min(10vw,14px)]">
                    <SelectValue placeholder="Select Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category: Category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <div className="bg-white border w-full h-auto rounded-lg">
            <p className="w-full text-[min(10vw,17px)] text-left p-5 border-b">
              Other Details
            </p>
            <div className="grid grid-cols-1 sm3:grid-cols-2 gap-8 px-8 py-5 w-full xl:w-[80%]">
              <div className="w-full">
                <label className="block text-[min(10vw,15px)] text-gray-600">
                  Stock (Kg)
                </label>
                <input
                  type="number"
                  name="stock"
                  value={stock || ''}
                  onChange={e => setStock(Number(e.target.value))}
                  className="w-full px-4 py-2 border rounded bg-white text-[min(10vw,14px)] mt-1"
                  required
                />
              </div>
              <div className="w-full">
                <label className="block text-[min(10vw,15px)] text-gray-600">
                  Minimum Order Quantity (Kg)
                </label>
                <input
                  type="number"
                  name="minOrderQuantity"
                  value={minOrderQuantity || ''}
                  onChange={e => setMinOrderQuantity(Number(e.target.value))}
                  className="w-full px-4 text-[min(10vw,14px)] mt-1 py-2 border rounded bg-white"
                  required
                />
              </div>
              <div className="">
                <label className="block text-[min(10vw,15px)] text-gray-600">
                  Harvest Date
                </label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full text-[min(10vw,14px)] mt-1 justify-start text-left"
                    >
                      {selectedDate
                        ? format(selectedDate, 'PPP')
                        : 'Pick a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={setSelectedDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          <div className="mt-6 text-right">
            <Button
              onClick={() => router.back()}
              className="md:text-md mb-2 items-center mr-4 text-eweko_green_light bg-transparent shadow-none hover:bg-transparent text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              className="bg-eweko_green_dark text-white text-[17px] py-4 px-6 rounded-lg"
            >
              Update Produce
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
