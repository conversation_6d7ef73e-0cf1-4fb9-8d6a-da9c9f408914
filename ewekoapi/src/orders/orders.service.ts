import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateOrderDto, OrderResponseDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { User } from '../users/entities/user.entity';
import { Order, OrderItem, OrderStatus } from './entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { UsersService } from 'src/users/users.service';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepository: Repository<OrderItem>,
    @InjectRepository(Produce)
    private readonly produceRepository: Repository<Produce>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly userService: UsersService,
    private readonly paginationService: PaginationService,
  ) {}

  // Create a new order
  async create(createOrderDto: CreateOrderDto) {
    // Verify if the user exists
    const user = await this.userService.findById(createOrderDto.userId);
    if (!user) {
      throw new NotFoundException(`User not found`);
    }

    let orderItems = [];

    // Verify if all items in the order exist and are available
    for (const item of createOrderDto.items) {
      const produce = await this.produceRepository.findOne({
        where: { id: item.produce }
      });
      if (!produce) {
        throw new NotFoundException(`Produce not found`);
      }

      // Optional: Add more checks, like verifying if enough stock is available
      if (produce.stock < item.quantity) {
        throw new BadRequestException(
          `Not enough stock for produce with id ${item.produce}`,
        );
      }
      const orderItem = this.orderItemRepository.create({
        produce_id: item.produce,
        quantity: item.quantity,
        total_price: item.totalPrice,
      });
      const savedOrderItem = await this.orderItemRepository.save(orderItem);
      orderItems.push(savedOrderItem);
    }

    // Calculate final total cost including shipping fee
    const finalTotalCost =
      createOrderDto.totalCost + createOrderDto.shippingFee;

    // Create the order document
    const createdOrder = this.orderRepository.create({
      buyer_id: createOrderDto.userId,
      farmer_id: orderItems[0]?.produce?.farmer_id || null, // Get farmer from first item
      total_cost: createOrderDto.totalCost,
      shipping_fee: createOrderDto.shippingFee,
      shipping_address: createOrderDto.shippingAddress || null,
      final_total_cost: createOrderDto.totalCost + (createOrderDto.shippingFee || 0),
      status: OrderStatus.PENDING,
    });

    // Save the order to the database
    const savedOrder = await this.orderRepository.save(createdOrder);

    // Associate order items with the order
    for (const orderItem of orderItems) {
      orderItem.order_id = savedOrder.id;
      await this.orderItemRepository.save(orderItem);
    }

    // Return the created order
    return savedOrder;
  }

  // Find all orders
  async findAll(paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Get total count
    const total = await this.orderRepository.count();

    // Fetch paginated data
    const orders = await this.orderRepository.find({
      relations: ['items', 'items.produce', 'user'],
      skip: (page - 1) * limit,
      take: limit,
      order: { created_at: 'DESC' }
    });

    // Return paginated response
    return this.paginationService.paginate(orders, {
      page,
      limit,
    });
  }

  // Find all orders for a specific buyer
  async findAllByBuyer(userId: string, paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Get total count for this buyer
    const total = await this.orderRepository.count({ where: { buyer_id: userId } });

    // Fetch paginated data for this buyer
    const orders = await this.orderRepository.find({
      where: { buyer_id: userId },
      relations: ['items', 'items.produce', 'user'],
      skip: (page - 1) * limit,
      take: limit,
      order: { created_at: 'DESC' }
    });

    // Return paginated response
    return this.paginationService.paginate(orders, {
      page,
      limit,
    });
  }

  // Find a specific order by ID
  async findOne(id: string) {
    const order = await this.orderRepository.findOne({
      where: { id },
      relations: ['items', 'items.produce', 'user']
    });
    if (!order) {
      throw new NotFoundException(`Order not found`);
    }
    return order;
  }

  // Update an existing order
  async update(id: string, updateOrderDto: UpdateOrderDto) {
    const order = await this.orderRepository.findOne({ where: { id } });
    if (!order) {
      throw new NotFoundException(`Order not found`);
    }

    // Transform DTO fields to database fields if needed
    const updateData: any = {};
    if (updateOrderDto.orderStatus) updateData.order_status = updateOrderDto.orderStatus;
    if (updateOrderDto.paymentStatus) updateData.payment_status = updateOrderDto.paymentStatus;
    // Note: shippingAddress is not available in UpdateOrderDto

    // Update the order details
    await this.orderRepository.update(id, updateData);

    // Return the updated order
    return this.findOne(id);
  }

  async deleteAllOrders(): Promise<{ message: string; deletedCount: number }> {
    const result = await this.orderRepository.delete({});
    return {
      message: 'All orders have been deleted',
      deletedCount: result.affected || 0,
    };
  }
}
