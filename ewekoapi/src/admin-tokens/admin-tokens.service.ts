import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateAdminTokenDto,
  RenewAdminTokenDto,
} from './dto/create-admin-token.dto';
import { AdminToken } from './entities/admin-token.entity';
import { UsersService } from 'src/users/users.service';
import { add, addMinutes, isBefore, isEqual } from 'date-fns';

@Injectable()
export class AdminTokensService {
  constructor(
    @InjectRepository(AdminToken)
    private adminTokenRepository: Repository<AdminToken>,
    private userService: UsersService,
  ) {}

  async create(createAdminTokenDto: CreateAdminTokenDto): Promise<AdminToken> {
    const { adminId } = createAdminTokenDto;

    // Deactivate existing tokens for this admin
    await this.adminTokenRepository.update(
      { admin_id: adminId },
      { used: true }
    );

    const token = await this.generateAdminToken();

    const createdAdminToken = this.adminTokenRepository.create({
      token,
      admin_id: adminId,
      expires_at: this.expiryDate(),
      used: false,
    });

    return await this.adminTokenRepository.save(createdAdminToken);
  }

  async renew(renewAdminTokenDto: RenewAdminTokenDto): Promise<AdminToken> {
    const { adminToken } = renewAdminTokenDto;

    const verifiedToken = await this.adminTokenRepository.findOne({
      where: { token: adminToken, used: false },
      relations: ['admin']
    });

    if (!verifiedToken) {
      throw new NotFoundException(
        `AdminToken with token ${adminToken} not found`,
      );
    }

    if (verifiedToken.expires_at < new Date()) {
      throw new BadRequestException('Token has expired');
    }

    // Mark old token as used
    await this.adminTokenRepository.update(
      { id: verifiedToken.id },
      { used: true }
    );

    const token = await this.generateAdminToken();

    const createdAdminToken = this.adminTokenRepository.create({
      token,
      admin_id: verifiedToken.admin_id,
      expires_at: this.expiryDate(),
      used: false,
    });

    return await this.adminTokenRepository.save(createdAdminToken);
  }

  async findAll(): Promise<AdminToken[]> {
    return await this.adminTokenRepository.find({
      relations: ['admin'],
      order: { created_at: 'DESC' }
    });
  }

  async findOne(id: string): Promise<AdminToken> {
    const adminToken = await this.adminTokenRepository.findOne({
      where: { id },
      relations: ['admin']
    });
    if (!adminToken) {
      throw new NotFoundException(`AdminToken with ID ${id} not found`);
    }
    return adminToken;
  }

  async deactivateToken(token: string): Promise<AdminToken> {
    const adminToken = await this.adminTokenRepository.findOne({
      where: { token }
    });

    if (!adminToken) {
      throw new NotFoundException(`AdminToken with token ${token} not found`);
    }

    await this.adminTokenRepository.update(
      { id: adminToken.id },
      { used: true }
    );

    return await this.adminTokenRepository.findOne({
      where: { id: adminToken.id }
    });
  }

  async deleteToken(token: string): Promise<void> {
    const result = await this.adminTokenRepository.delete({ token });

    if (result.affected === 0) {
      throw new NotFoundException(`AdminToken with token ${token} not found`);
    }
  }

  private async generateAdminToken(): Promise<string> {
    const characters = 'EWEKO0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let token = '3WK';

    for (let i = 0; i < 13; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      token += characters[randomIndex];
    }

    return token;
  }

  private expiryDate = (): Date => {
    const createdAt = new Date();
    return add(createdAt, { months: 3 });
  };

  private isTokenExpired(expiry: Date): boolean {
    const currentDate = new Date();
    return isBefore(expiry, currentDate) || isEqual(expiry, currentDate);
  }
}
