import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsArray,
  IsMongoId,
  IsPhoneNumber,
  Matches,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { UserType, AdminRole } from 'src/shared/enums';

export class UpdateUserDto {
  @ApiProperty({
    description: 'Profile picture URL',
    example: 'https://example.com/images/profile.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsUrl()
  profilePicture?: string;

  @ApiProperty({
    description: 'First name of the user',
    example: 'John',
    required: false,
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({
    description: 'Last name of the user',
    example: 'Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: 'Phone number of the user',
    example: '+2348034023726',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsPhoneNumber()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Password of the user',
    example: 'SecureP@ssword123',
    required: false,
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'User type',
    example: UserType.BUYER,
    enum: UserType,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserType)
  userType?: UserType;

  @ApiProperty({
    description: 'User verified status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @ApiProperty({
    description: 'Phone verification status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPhoneVerified?: boolean;

  @ApiProperty({
    description: 'User active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Farm name of the farmer (applicable for Farmers only)',
    example: 'Sunny Farm',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmName?: string;

  @ApiProperty({
    description: 'Farm address',
    example: '26B, Aleniboro, Lagos, Nigeria',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmAddress?: string;

  @ApiProperty({
    description: 'Farm size',
    example: '2096 Hectares',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmSize?: string;

  @ApiProperty({
    description: 'Bank account number (applicable for Farmers only)',
    example: '**********',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmerAccountNumber?: string;

  @ApiProperty({
    description: "Account holder's name (applicable for Farmers only)",
    example: 'John Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmerAccountName?: string;

  @ApiProperty({
    description: 'Bank name (applicable for Farmers only)',
    example: 'First Bank of Nigeria',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmerBankName?: string;

  @ApiProperty({
    description: 'Branch name (applicable for Farmers only)',
    example: 'Victoria Island Branch',
    required: false,
  })
  @IsOptional()
  @IsString()
  farmerBankBranch?: string;

  @ApiProperty({
    description: 'Role of the admin (applicable for Admins only)',
    example: AdminRole.SUPER_ADMIN,
    enum: AdminRole,
    required: false,
  })
  @IsOptional()
  @IsEnum(AdminRole)
  adminRole?: AdminRole;

  @ApiProperty({
    description: 'Admin token (applicable for Admins only)',
    example: 'adminToken12345',
    required: false,
  })
  @IsOptional()
  @IsString()
  adminToken?: string;
}

export class UpdateProfileDto {
  @ApiPropertyOptional({ example: 'John' })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({ example: 'Doe' })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({ example: 'Middle' })
  @IsOptional()
  @IsString()
  middleName?: string;

  @ApiPropertyOptional({ example: 'Mr.' })
  @IsOptional()
  @IsString()
  prefix?: string;

  @ApiPropertyOptional({ example: 'male', enum: ['male', 'female', 'other'] })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiPropertyOptional({ example: '1995-12-31' })
  @IsOptional()
  dateOfBirth?: Date;

  @ApiPropertyOptional({
    example: 'https://example.com/profile-pic.jpg',
  })
  @IsOptional()
  @IsString()
  profilePicture?: string;
}

export class UpdateContactDto {
  @ApiPropertyOptional({ example: '+2348034023726' })
  @IsOptional()
  @IsPhoneNumber()
  primaryPhone?: string;

  @ApiPropertyOptional({ example: '+2348012345678' })
  @IsOptional()
  @IsPhoneNumber()
  secondaryPhone?: string;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  email?: string;
}

export class UpdateBusinessDto {
  @ApiPropertyOptional({ example: 'Onimifresh Stores' })
  @IsOptional()
  @IsString()
  businessName?: string;
}

export class UpdateBuyerDto {
  @ApiPropertyOptional({ example: 'newusername123' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({
    example: 'NewStrongP@ss1',
    description:
      'Password must be 4-40 characters, with one uppercase, one lowercase, one number, and one special character.',
  })
  @IsOptional()
  @IsString()
  @MinLength(4)
  @MaxLength(40)
  @Matches(
    /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?/\\|`~])/,
    {
      message:
        'Password must contain uppercase, lowercase, number, and special character.',
    },
  )
  password?: string;

  @ApiPropertyOptional({ type: UpdateProfileDto })
  @IsOptional()
  profile?: UpdateProfileDto;

  @ApiPropertyOptional({ type: UpdateContactDto })
  @IsOptional()
  contact?: UpdateContactDto;

  @ApiPropertyOptional({ type: UpdateBusinessDto })
  @IsOptional()
  business?: UpdateBusinessDto;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ example: '2025-04-10T08:00:00.000Z' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  lastLogin?: Date;
}

export class UpdateFarmerDto {
  @ApiPropertyOptional({ example: 'newusername123' })
  @IsOptional()
  @IsString()
  farmName?: string;

  @ApiPropertyOptional({ example: '123 Farm Street, Community, State' })
  @IsOptional()
  @IsString()
  farmAddress?: string;

  @ApiPropertyOptional({ example: '3.5', description: 'Farm size in acres' })
  @IsOptional()
  @IsString()
  farmSize?: string;

  @ApiPropertyOptional({ example: '**********' })
  @IsOptional()
  @IsString()
  farmerAccountNumber?: string;

  @ApiPropertyOptional({ example: 'Eweko John' })
  @IsOptional()
  @IsString()
  farmerAccountName?: string;

  @ApiPropertyOptional({ example: 'Access Bank' })
  @IsOptional()
  @IsString()
  farmerBankName?: string;

  @ApiPropertyOptional({ example: 'Lagos Mainland Branch' })
  @IsOptional()
  @IsString()
  farmerBankBranch?: string;

  @ApiPropertyOptional({ example: "Eweko's Farm" })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({
    example: 'NewStrongP@ss1',
    description:
      'Password must be 4-40 characters, with one uppercase, one lowercase, one number, and one special character.',
  })
  @IsOptional()
  @IsString()
  @MinLength(4)
  @MaxLength(40)
  @Matches(
    /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?/\\|`~])/,
    {
      message:
        'Password must contain uppercase, lowercase, number, and special character.',
    },
  )
  password?: string;

  @ApiPropertyOptional({ type: UpdateProfileDto })
  @IsOptional()
  profile?: UpdateProfileDto;

  @ApiPropertyOptional({ type: UpdateContactDto })
  @IsOptional()
  contact?: UpdateContactDto;

  @ApiPropertyOptional({ type: UpdateBusinessDto })
  @IsOptional()
  business?: UpdateBusinessDto;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ example: '2025-04-10T08:00:00.000Z' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  lastLogin?: Date;
}
