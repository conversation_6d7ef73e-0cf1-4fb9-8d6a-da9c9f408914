import { IsString, IsEmail, IsEnum, IsOptional, IsBoolean, IsDateString } from 'class-validator';
import { UserType } from '../../shared/enums';

export class CreateUserDto {
  // Authentication fields
  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsEnum(UserType)
  userType: UserType;

  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  // Profile fields (now in main users table)
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  middleName?: string;

  @IsOptional()
  @IsString()
  prefix?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsDateString()
  dateOfBirth?: Date;

  @IsOptional()
  @IsString()
  profilePicture?: string;

  // Contact fields - email only (phones and addresses in separate tables)
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  primaryPhone?: string;

  // Business fields (now in main users table)
  @IsOptional()
  @IsString()
  businessName?: string;

  // Common fields
  @IsOptional()
  @IsBoolean()
  isPremium?: boolean;

  @IsOptional()
  @IsBoolean()
  isPhoneVerified?: boolean;

  @IsOptional()
  @IsBoolean()
  isEmailVerified?: boolean;
}

export class CreateAddressDto {
  @IsString()
  street: string;

  @IsString()
  city: string;

  @IsString()
  state: string;

  @IsString()
  country: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsOptional()
  @IsString()
  type?: string; // 'home', 'work', 'delivery', 'billing', 'other'

  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;
}

export class CreateBuyerDto extends CreateUserDto {
  constructor() {
    super();
    this.userType = UserType.BUYER;
  }
}

export class CreateFarmerDto extends CreateUserDto {
  @IsOptional()
  @IsString()
  farmName?: string; // Maps to business_name

  constructor() {
    super();
    this.userType = UserType.FARMER;
  }
}

// Response DTOs
export class BuyerResponseDto {
  id: string;
  username: string;
  userType: UserType;
  firstName: string;
  lastName: string;
  email: string;
  primaryPhone?: string;
  secondaryPhone?: string;
  addresses?: CreateAddressDto[];
}

export class FarmerResponseDto {
  id: string;
  username: string;
  userType: UserType;
  firstName: string;
  lastName: string;
  email: string;
  primaryPhone?: string;
  secondaryPhone?: string;
  addresses?: CreateAddressDto[];
}

export class UserStatsDto {
  totalUsers: number;
  totalBuyers: number;
  totalFarmers: number;
  totalAdmins: number;
}

export class UserWithTypeDto {
  id: string;
  username: string;
  type: UserType;
  verified: boolean;
  isActive: boolean;
  firstName: string;
  lastName: string;
  email: string;
  addresses?: CreateAddressDto[];
}
