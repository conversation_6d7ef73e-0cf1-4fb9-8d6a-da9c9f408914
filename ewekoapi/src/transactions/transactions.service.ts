import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { UpdateTransactionDto } from './dto/update-transaction.dto';
import { ConfigService } from '@nestjs/config';
import { randomBytes } from 'crypto';
import { InjectRepository } from '@nestjs/typeorm';
import { Transaction, TransactionStatus } from './entities/transaction.entity';
import { Order } from '../orders/entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { User } from '../users/entities/user.entity';
import { Buyer } from '../users/entities/buyers/buyer.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { PaymentStatus } from 'src/shared/enums';
import { OrdersService } from 'src/orders/orders.service';
import { UsersService } from 'src/users/users.service';
import { PaginationQueryDto } from 'src/shared/pagination/pagination-query.dto';
import { PaginationService } from 'src/shared/pagination/pagination.service';

@Injectable()
export class TransactionsService {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UsersService,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Produce)
    private readonly produceRepository: Repository<Produce>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Buyer)
    private readonly buyerRepository: Repository<Buyer>,
    @InjectRepository(Farmer)
    private readonly farmerRepository: Repository<Farmer>,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createTransactionDto: CreateTransactionDto) {
    const { user, order, invoice } = createTransactionDto;

    const existingUser = await this.userRepository.findOne({
      where: { id: user }
    });
    if (!existingUser) throw new NotFoundException('User not found');

    const existingOrder = await this.orderRepository.findOne({
      where: { id: order }
    });
    if (!existingOrder) throw new NotFoundException('Order not found');

    // if (invoice) {
    //   const existingInvoice = await this.invoiceModel.findById(invoice);
    //   if (!existingInvoice) throw new NotFoundException('Invoice not found');
    // }

    const transaction = this.transactionRepository.create({
      user_id: user,
      buyer_id: user,
      farmer_id: existingOrder.farmer_id,
      order_id: order,
      total_amount: createTransactionDto.totalAmount,
      payment_method: createTransactionDto.paymentMethod as any,
      reference: this.generateTransactionReference(),
      status: TransactionStatus.PENDING,
    });

    const savedTrx = await this.transactionRepository.save(transaction);

    return savedTrx;
  }

  async findAll(paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    // Get total count
    const total = await this.transactionRepository.count();

    // Fetch paginated data
    const transactions = await this.transactionRepository.find({
      skip: (page - 1) * limit,
      take: limit,
      relations: ['buyer', 'farmer', 'order'],
      order: { created_at: 'DESC' }
    });

    // Return paginated response
    return this.paginationService.paginate(transactions, {
      page,
      limit,
    });
  }

  async findAllByFarmer(farmerId: string, paginationQuery: PaginationQueryDto) {
    const { page = 1, limit = 10 } = paginationQuery;

    try {
      // Find transactions for this farmer
      const transactions = await this.transactionRepository.find({
        where: { farmer_id: farmerId },
        relations: ['buyer', 'farmer', 'order'],
        skip: (page - 1) * limit,
        take: limit,
        order: { created_at: 'DESC' }
      });

      // Return paginated response
      return this.paginationService.paginate(transactions, {
        page,
        limit,
      });

    } catch (error) {
      console.error('Error finding farmer transactions:', error);
      throw error;
    }
  }

  async findAllByBuyer(
    buyerId: string,
    paginationQuery: PaginationQueryDto,
  ): Promise<any> {
    const { page = 1, limit = 10 } = paginationQuery;

    try {
      // Fetch paginated transactions for this buyer
      const buyerTransactions = await this.transactionRepository.find({
        where: { buyer_id: buyerId },
        relations: ['buyer', 'farmer', 'order'],
        skip: (page - 1) * limit,
        take: limit,
        order: { created_at: 'DESC' }
      });

      // Return paginated response
      return this.paginationService.paginate(buyerTransactions, {
        page,
        limit,
      });
    } catch (error) {
      console.error('Error finding buyer transactions:', error);
      throw error;
    }
  }

  async findOne(id: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { id }
    });
    if (!transaction) {
      throw new NotFoundException(`Transaction not found`);
    }
    return transaction;
  }

  async update(
    id: string,
    updateTransactionDto: UpdateTransactionDto,
  ): Promise<Transaction> {
    const result = await this.transactionRepository.update(id, updateTransactionDto);
    if (result.affected === 0) {
      throw new NotFoundException(`Transaction with id ${id} not found`);
    }
    const transaction = await this.transactionRepository.findOne({ where: { id } });

    if (!transaction) {
      throw new NotFoundException(`Transaction not found`);
    }

    return transaction;
  }

  async deleteAllTransactions(): Promise<{
    message: string;
    deletedCount: number;
  }> {
    const result = await this.transactionRepository.delete({});
    return {
      message: 'All transactions have been deleted',
      deletedCount: result.affected || 0,
    };
  }

  private generateTransactionReference(): string {
    return `EWKTRX-${uuidv4().replace(/-/g, '').slice(0, 12).toUpperCase()}`;
  }
}
