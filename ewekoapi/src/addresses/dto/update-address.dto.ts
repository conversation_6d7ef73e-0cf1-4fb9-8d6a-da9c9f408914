import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateAddressDto {
  @ApiProperty({
    description: 'The ID of the user this address belongs to.',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'House or building number.',
    example: '12A',
    required: false,
  })
  @IsString()
  @IsOptional()
  houseNumber?: string;

  @ApiProperty({
    description: 'Street name of the address.',
    example: 'Main Street',
    required: false,
  })
  @IsString()
  @IsOptional()
  streetName?: string;

  @ApiProperty({
    description: 'Local Government Area (LGA) of the address.',
    example: 'Ikeja',
    required: false,
  })
  @IsString()
  @IsOptional()
  lga?: string;

  @ApiProperty({
    description: 'State where the address is located.',
    example: 'Lagos',
    required: false,
  })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty({
    description: 'Country where the address is located.',
    example: 'Nigeria',
    required: false,
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: 'Community where the address is located (optional).',
    example: 'Community A',
    required: false,
  })
  @IsString()
  @IsOptional()
  community?: string;

  @ApiProperty({
    description: 'Indicates whether this is the default address.',
    example: true,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}
